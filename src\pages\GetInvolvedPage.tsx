import React from 'react';
import { Users, Heart, MessageSquare, Briefcase, ChevronRight, UserCircle } from 'lucide-react';

interface InvolvementCardProps {
  icon: React.ReactNode;
  title: string;
  description: string;
  items: string[];
  buttonText: string;
  buttonLink?: string;
  imageUrl?: string; // Added for background image
}

const InvolvementCard: React.FC<InvolvementCardProps> = ({ icon, title, description, items, buttonText, buttonLink = "#", imageUrl }) => (
  <div 
    className="rounded-xl shadow-xl hover:shadow-2xl transition-all duration-300 p-8 flex flex-col relative overflow-hidden min-h-[420px] group border border-gray-200/30"
    style={imageUrl ? { backgroundImage: `url(${imageUrl})`, backgroundSize: 'cover', backgroundPosition: 'center' } : { backgroundColor: '#ffffff' }}
  >
    {/* Overlay for text contrast if image is present */}
    {imageUrl && <div className="absolute inset-0 bg-black/60 group-hover:bg-black/70 transition-colors duration-300"></div>}
    
    <div className={`relative z-10 flex flex-col h-full ${imageUrl ? 'text-white' : 'text-gray-900'}`}>
      <div className="flex items-center mb-6">
        <div className={`p-3 rounded-lg mr-4 ${imageUrl ? 'bg-white/20 backdrop-blur-sm' : 'bg-primary-100'}`}>
          {React.cloneElement(icon as React.ReactElement, { size: 32, className: imageUrl ? 'text-white' : 'text-primary-600' })}
        </div>
        <h3 className={`text-2xl font-bold ${imageUrl ? 'text-white' : 'text-gray-900'}`}>{title}</h3>
      </div>
      <p className={`mb-6 flex-grow ${imageUrl ? 'text-neutral-200' : 'text-gray-600'}`}>{description}</p>
      <ul className="space-y-2 mb-6">
        {items.map((item, index) => (
          <li key={index} className={`flex items-center ${imageUrl ? 'text-neutral-100' : 'text-gray-700'}`}>
            <ChevronRight size={18} className={`mr-2 ${imageUrl ? 'text-primary-300' : 'text-primary-500'}`} />
            {item}
          </li>
        ))}
      </ul>
      <a
        href={buttonLink}
        className={`w-full text-center py-3 px-6 rounded-lg font-semibold transition-all duration-300 mt-auto ${
          imageUrl 
            ? 'bg-white/90 text-primary-700 hover:bg-white transform hover:scale-105' 
            : 'bg-primary-600 text-white hover:bg-primary-700 transform hover:scale-105'
        }`}
      >
        {buttonText}
      </a>
    </div>
  </div>
);

const GetInvolvedPage: React.FC = () => {
  const involvementOptions: InvolvementCardProps[] = [
    {
      icon: <Users />,
      title: 'Volunteer',
      description: 'Join our team of dedicated volunteers and contribute your time and skills to make a direct impact.',
      items: ['Event support & coordination', 'Mentorship programs', 'Community outreach', 'Administrative assistance'],
      buttonText: 'Become a Volunteer',
      imageUrl: '/images/a1.jpg',
    },
    {
      icon: <Heart />,
      title: 'Donate',
      description: 'Your generous contributions help us fund critical programs, support services, and expand our reach.',
      items: ['One-time donations', 'Monthly giving program', 'Sponsor a program', 'In-kind contributions'],
      buttonText: 'Make a Donation',
      imageUrl: '/images/a2.jpg',
    },
    {
      icon: <MessageSquare />,
      title: 'Advocate',
      description: 'Use your voice to raise awareness, influence policy, and support efforts addressing drug abuse and trafficking.',
      items: ['Share our mission on social media', 'Contact your representatives', 'Host an awareness event', 'Join advocacy campaigns'],
      buttonText: 'Start Advocating',
      imageUrl: '/images/a3.jpg',
    },
    {
      icon: <Briefcase />,
      title: 'Partner With Us',
      description: 'Collaborate with MADAST as an organization, corporation, or community group to amplify our impact.',
      items: ['Corporate sponsorships', 'Community partnerships', 'Program collaborations', 'Resource sharing'],
      buttonText: 'Explore Partnerships',
      imageUrl: '/images/a4.jpg',
    },
  ];

  return (
    <>
      {/* Hero Section - Enhanced */}
      <section className="relative text-white py-24 md:py-32 bg-cover bg-center" style={{ backgroundImage: "url('/images/a4.jpg')" }}>
        <div className="absolute inset-0 bg-black opacity-50"></div> {/* Overlay for contrast */}
        <div className="container mx-auto px-6 text-center relative z-10"> {/* Ensure content is above overlay */}
          <h1 className="text-4xl sm:text-5xl md:text-6xl font-bold mb-6 leading-tight" style={{ textShadow: '2px 2px 8px rgba(0,0,0,0.5)' }}>
            Get Involved
          </h1>
          <p className="text-lg md:text-xl max-w-3xl mx-auto text-neutral-100 leading-relaxed" style={{ textShadow: '1px 1px 3px rgba(0,0,0,0.4)' }}> {/* Adjusted shadow for better visibility */}
            There are many ways to join our mission and make a meaningful impact in addressing drug abuse and human trafficking.
          </p>
        </div>
      </section>

      {/* Ways to Get Involved - Enhanced */}
      <section className="py-16 md:py-24 bg-slate-50"> {/* Changed background for contrast */}
        <div className="container mx-auto px-6">
          <div className="text-center mb-14 md:mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-800 title-underline-white mb-3">Ways to Make a Difference</h2> {/* Removed text-gradient-animated */}
            <p className="text-lg text-neutral-600 mt-2 max-w-3xl mx-auto leading-relaxed">
              Choose the way that best fits your passion, skills, and availability. Every contribution helps create safer communities.
            </p>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 lg:gap-10">
            {involvementOptions.map(option => (
              <InvolvementCard key={option.title} {...option} />
            ))}
          </div>
        </div>
      </section>




    </>
  );
};

export default GetInvolvedPage;
