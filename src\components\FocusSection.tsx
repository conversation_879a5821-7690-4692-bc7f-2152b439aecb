import React from 'react';
import { <PERSON>, <PERSON>, Users, BookOpen, X, Handshake } from 'lucide-react';

const FocusSection: React.FC = () => {
  const focusAreas = [
    {
      icon: <BookOpen className="w-8 h-8 text-blue-600" />,
      title: 'Education & Enlightenment',
      description: 'Providing education and enlightenment programs against drug abuse and sex trafficking.'
    },
    {
      icon: <Heart className="w-8 h-8 text-red-600" />,
      title: 'Intervention Programs',
      description: 'Providing intervention programs for victims of drug abuse and sex trafficking.'
    },
    {
      icon: <X className="w-8 h-8 text-green-600" />,
      title: 'Treatment Facilities',
      description: 'Operating detox and rehab treatment facilities for victims of drug abuse and sex trafficking.'
    },
    {
      icon: <Shield className="w-8 h-8 text-purple-600" />,
      title: 'Medical Support',
      description: 'Transporting victims to hospitals and health centers for their treatments and medical appointments.'
    },
    {
      icon: <Handshake className="w-8 h-8 text-orange-600" />,
      title: 'Strategic Partnerships',
      description: 'Partnering with governments, health centers, and charitable organizations, and public-spirited individuals to care for drug abuse and sex trafficking victims.'
    },
    {
      icon: <Users className="w-8 h-8 text-indigo-600" />,
      title: 'Community Action',
      description: 'Performing related functions to reduce the incidence of drug abuse and sex trafficking.'
    }
  ];

  return (
    <section className="py-16 md:py-24 bg-gradient-to-br from-slate-50 via-blue-50 to-purple-50 relative overflow-hidden">
      {/* Background decoration */}
      <div className="absolute inset-0 bg-gradient-to-r from-blue-100/20 to-purple-100/20"></div>
      <div className="absolute top-0 left-0 w-96 h-96 bg-blue-200/10 rounded-full -translate-x-48 -translate-y-48"></div>
      <div className="absolute bottom-0 right-0 w-96 h-96 bg-purple-200/10 rounded-full translate-x-48 translate-y-48"></div>
      
      <div className="container mx-auto px-6 relative z-10">
        {/* Two Column Layout */}
        <div className="grid lg:grid-cols-2 gap-12 lg:gap-16 items-center mb-16">
          {/* Left Column - Image */}
          <div className="relative">
            <div className="relative rounded-2xl overflow-hidden shadow-2xl transform hover:scale-105 transition-transform duration-500">
              <img 
                src="/images/a1.jpg" 
                alt="Faith communities working together" 
                className="w-full h-[500px] object-cover"
              />
              <div className="absolute inset-0 bg-gradient-to-t from-black/40 via-transparent to-transparent"></div>
              <div className="absolute bottom-6 left-6 right-6">
                <div className="bg-white/90 backdrop-blur-sm rounded-xl p-4 shadow-lg">
                  <h3 className="text-lg font-bold text-gray-800 mb-1">Transforming Lives</h3>
                  <p className="text-sm text-gray-600">Through compassionate action and community support</p>
                </div>
              </div>
            </div>
            {/* Floating elements */}
            <div className="absolute -top-4 -right-4 w-20 h-20 bg-blue-500 rounded-full opacity-20 animate-pulse"></div>
            <div className="absolute -bottom-4 -left-4 w-16 h-16 bg-purple-500 rounded-full opacity-20 animate-pulse delay-1000"></div>
          </div>

          {/* Right Column - Content */}
          <div className="space-y-8">
            <div>
              <h2 className="text-4xl md:text-5xl font-bold text-gray-800 mb-6 leading-tight">
                Empowering Faith, 
                <span className="text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-purple-600"> Ending Exploitation</span>, 
                and Transforming Lives
              </h2>
              <p className="text-lg text-gray-600 leading-relaxed mb-6">
                At the heart of our endeavor is a commitment to reduce the incidence of drug abuse and sex trafficking. MADAST operates exclusively to educate, create awareness, sensitize, volunteer and work against drug abuse and child trafficking.
              </p>
              <div className="bg-blue-50/80 backdrop-blur-sm rounded-xl p-6 mb-8 border border-blue-100">
                <p className="text-base text-gray-700 leading-relaxed mb-3">
                  <strong className="text-blue-800">Our nonprofit is not just for faith leaders and religious organizations; we serve the public through our faith-based initiatives.</strong>
                </p>
                <p className="text-base text-gray-700 leading-relaxed">
                  <strong className="text-blue-800">We collaborate with a wide range of partners,</strong> including government agencies, civil society organizations, schools, teachers, social workers, foundations, philanthropic organizations, the media, health workers, and other nonprofits.
                </p>
              </div>
            </div>

            {/* Key Stats or Highlights */}
            <div className="grid grid-cols-2 gap-6">
              <div className="bg-white/70 backdrop-blur-sm rounded-xl p-6 shadow-lg border border-white/50">
                <div className="text-3xl font-bold text-blue-600 mb-2">6</div>
                <div className="text-sm font-medium text-gray-700">Core Focus Areas</div>
              </div>
              <div className="bg-white/70 backdrop-blur-sm rounded-xl p-6 shadow-lg border border-white/50">
                <div className="text-3xl font-bold text-purple-600 mb-2">24/7</div>
                <div className="text-sm font-medium text-gray-700">Support Available</div>
              </div>
            </div>

            {/* Call to Action */}
            <div className="flex flex-col sm:flex-row gap-4">
              <a 
                href="/get-involved" 
                className="inline-flex items-center justify-center px-8 py-4 bg-gradient-to-r from-blue-600 to-purple-600 text-white font-semibold rounded-xl shadow-lg hover:shadow-xl transform hover:-translate-y-1 transition-all duration-300"
              >
                Join Our Mission
              </a>
              <a 
                href="/about" 
                className="inline-flex items-center justify-center px-8 py-4 bg-white/80 backdrop-blur-sm text-gray-800 font-semibold rounded-xl shadow-lg hover:shadow-xl border border-gray-200 transform hover:-translate-y-1 transition-all duration-300"
              >
                Learn More
              </a>
            </div>
          </div>
        </div>

        {/* Focus Areas Grid */}
        <div className="space-y-12">
          <div className="text-center">
            <h3 className="text-3xl md:text-4xl font-bold text-gray-800 mb-4">Our Core Focus Areas</h3>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              We work across multiple dimensions to create lasting change and provide comprehensive support to those affected by exploitation.
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {focusAreas.map((area, index) => (
              <div 
                key={index}
                className="group bg-white/80 backdrop-blur-sm rounded-2xl p-8 shadow-lg hover:shadow-2xl border border-white/50 transform hover:-translate-y-2 transition-all duration-300"
              >
                <div className="flex items-center mb-6">
                  <div className="p-3 bg-gradient-to-br from-gray-50 to-gray-100 rounded-xl shadow-md group-hover:shadow-lg transition-shadow duration-300">
                    {area.icon}
                  </div>
                </div>
                <h4 className="text-xl font-bold text-gray-800 mb-4 group-hover:text-blue-600 transition-colors duration-300">
                  {area.title}
                </h4>
                <p className="text-gray-600 leading-relaxed">
                  {area.description}
                </p>
              </div>
            ))}
          </div>
        </div>

        {/* Partnership Section */}
        <div className="mt-20 pt-16 border-t border-gray-200/50">
          <div className="text-center mb-12">
            <h3 className="text-3xl md:text-4xl font-bold text-gray-800 mb-4">Our Diverse Partnership Network</h3>
            <p className="text-lg text-gray-600 max-w-4xl mx-auto">
              We collaborate with government agencies, civil society organizations, schools, health workers, foundations, media, and nonprofits to create a comprehensive network of support and advocacy.
            </p>
          </div>

          <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
            {[
              { name: 'Government Agencies', icon: '🏛️' },
              { name: 'Educational Institutions', icon: '🎓' },
              { name: 'Healthcare Partners', icon: '🏥' },
              { name: 'Civil Society Organizations', icon: '🤝' },
              { name: 'Philanthropic Foundations', icon: '💝' },
              { name: 'Media Organizations', icon: '📺' },
              { name: 'Social Workers Network', icon: '👥' },
              { name: 'Faith Communities', icon: '⛪' }
            ].map((partner, index) => (
              <div
                key={index}
                className="group bg-white/60 backdrop-blur-sm rounded-xl p-6 shadow-md hover:shadow-lg border border-white/50 transform hover:-translate-y-1 transition-all duration-300 text-center"
              >
                <div className="text-3xl mb-3 group-hover:scale-110 transition-transform duration-300">
                  {partner.icon}
                </div>
                <h4 className="text-sm font-semibold text-gray-700 group-hover:text-blue-600 transition-colors duration-300">
                  {partner.name}
                </h4>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
};

export default FocusSection;
