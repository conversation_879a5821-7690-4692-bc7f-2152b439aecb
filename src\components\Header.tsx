import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom'; // Import Link
import { NavItem } from '../types/types';
import { Globe } from 'lucide-react';

const navItems: NavItem[] = [
  { label: 'Home', href: '/' }, 
  { label: 'About', href: '/about' }, 
  { label: 'Programs', href: '/programs' }, 
  { label: 'Resources', href: '/resources' },
  { label: 'Get Involved', href: '/get-involved' }, // Updated href
  { label: 'Contact', href: '/contact' },
];

const Header: React.FC = () => {
  const [isScrolled, setIsScrolled] = useState(false);
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      const scrollPosition = window.scrollY;
      setIsScrolled(scrollPosition > 50);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  return (
    <header
      className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${
        isScrolled
          ? 'bg-white shadow-md py-2'
          : 'bg-transparent py-4'
      }`}
    >
      <div className="container mx-auto px-4 flex justify-between items-center">
        <Link to="/" className="flex items-center gap-2 font-bold text-xl">
          <Globe size={24} className={`transition-colors duration-300 ${isScrolled ? 'text-primary-600' : 'text-white'}`} />
          <span className={`transition-colors duration-300 ${isScrolled ? 'text-primary-700' : 'text-white'}`}>
            Ministers Against DAST
          </span>
        </Link>

        {/* Desktop Navigation */}
        <nav className="hidden md:flex items-center space-x-6">
          {navItems.map((item) => (
            <Link // Changed to Link
              key={item.label}
              to={item.href} // Use 'to' prop for Link
              className={`font-medium transition-colors duration-300 hover:text-primary-500 ${
                isScrolled ? 'text-neutral-700' : 'text-white'
              }`}
            >
              {item.label}
            </Link>
          ))}
          <Link
            to="/contact#contact-form-section" // Link to contact form section
            className={`bg-primary-600 hover:bg-primary-700 text-white px-5 py-2.5 rounded-lg font-semibold transition-all duration-300 transform hover:scale-105 ${
              !isScrolled ? 'nav-donate-glow' : ''
            }`}
          >
            Donate
          </Link>
        </nav>

        {/* Mobile Menu Button */}
        <button 
          className="md:hidden p-2 rounded-md focus:outline-none"
          onClick={() => setIsMenuOpen(!isMenuOpen)}
        >
          <div className={`w-6 h-0.5 my-1 transition-all duration-300 ${isScrolled ? 'bg-neutral-700' : 'bg-white'} ${isMenuOpen ? 'transform rotate-45 translate-y-1.5' : ''}`}></div>
          <div className={`w-6 h-0.5 my-1 transition-all duration-300 ${isScrolled ? 'bg-neutral-700' : 'bg-white'} ${isMenuOpen ? 'opacity-0' : ''}`}></div>
          <div className={`w-6 h-0.5 my-1 transition-all duration-300 ${isScrolled ? 'bg-neutral-700' : 'bg-white'} ${isMenuOpen ? 'transform -rotate-45 -translate-y-1.5' : ''}`}></div>
        </button>
      </div>

      {/* Mobile Menu */}
      <div
        className={`absolute top-full left-0 right-0 bg-white shadow-md transition-all duration-300 md:hidden ${
          isMenuOpen ? 'opacity-100 visible' : 'opacity-0 invisible'
        }`}
      >
        <nav className="container mx-auto px-4 py-4 flex flex-col space-y-4">
          {navItems.map((item) => (
            <Link // Changed to Link
              key={item.label}
              to={item.href} // Use 'to' prop for Link
              className="font-medium text-neutral-700 hover:text-primary-500 transition-colors duration-300"
              onClick={() => setIsMenuOpen(false)}
            >
              {item.label}
            </Link>
          ))}
          <Link
            to="/contact#contact-form-section" // Link to contact form section
            className={`bg-primary-600 hover:bg-primary-700 text-white px-5 py-2.5 rounded-lg font-semibold transition-all duration-300 transform hover:scale-105 mt-2 ${
              !isScrolled ? 'nav-donate-glow' : '' // Apply glow if not scrolled (though less relevant for mobile menu usually)
            }`}
            onClick={() => setIsMenuOpen(false)}
          >
            Donate
          </Link>
        </nav>
      </div>
    </header>
  );
};

export default Header;
