          import React, { useState, useEffect } from 'react';
import { ResourceItem } from '../types/types';
import { FileText, BookOpen, ExternalLink, ArrowRight } from 'lucide-react';

const backgroundImages = [
  '/images/a1.jpg',
  '/images/a2.jpg',
  '/images/a3.jpg',
  '/images/a4.jpg',
  '/images/a5.jpg',
];

const resourcesData: ResourceItem[] = [
  {
    id: 'toolkits',
    title: 'Faith Leader Toolkits',
    description: 'Comprehensive resources for identifying and responding to domestic abuse and trafficking in your community.'
  },

  {
    id: 'policy',
    title: 'Policy Briefs',
    description: 'Current research and policy recommendations for effective advocacy and community action.'
  }
];

const ResourcesSection: React.FC = () => {
  const [currentBgImageIndex, setCurrentBgImageIndex] = useState(0);

  useEffect(() => {
    const intervalId = setInterval(() => {
      setCurrentBgImageIndex((prevIndex) => (prevIndex + 1) % backgroundImages.length);
    }, 5000); // Change image every 5 seconds

    return () => clearInterval(intervalId);
  }, []);

  return (
    <section className="relative py-20 md:py-28 text-white overflow-hidden">
      {/* Background Image Slider */}
      {backgroundImages.map((image, index) => (
        <div
          key={index}
          className={`absolute inset-0 w-full h-full bg-cover bg-center transition-opacity duration-1000 ease-in-out -z-20 ${
            index === currentBgImageIndex ? 'opacity-100' : 'opacity-0'
          }`}
          style={{ backgroundImage: `url(${image})` }}
        />
      ))}
      {/* Overlay for text contrast */}
      <div className="absolute inset-0 bg-black/60 -z-10"></div>

      <div className="container mx-auto px-4 relative z-10">
        <h2 className="text-4xl md:text-5xl font-bold text-center text-gradient-silver mb-3">Resources</h2>
        <p className="text-center text-neutral-100 max-w-2xl mx-auto mb-12 md:mb-16 text-lg mt-2">
          Access our library of materials designed to equip faith leaders with the knowledge and resources needed to address exploitation.
        </p>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-5xl mx-auto">
          {resourcesData.map((resource) => (
            <div 
              key={resource.id}
              className="bg-white/20 backdrop-blur-lg rounded-xl p-6 md:p-8 shadow-xl hover:shadow-2xl transition-all duration-300 border border-white/30 group" // Added group for hover effects
            >
              <div className="bg-white/30 group-hover:bg-white/40 w-12 h-12 rounded-full flex items-center justify-center mb-5 ring-2 ring-white/40 transition-colors duration-300">
                {resource.id === 'toolkits' ? (
                  <FileText className="h-6 w-6 text-white" />
                ) : (
                  <ExternalLink className="h-6 w-6 text-white" />
                )}
              </div>
              <h3 className="text-xl font-semibold mb-3 text-white">{resource.title}</h3>
              <p className="text-neutral-200 mb-5 text-sm leading-relaxed">{resource.description}</p>
              <a 
                href="#" 
                className="inline-flex items-center text-white font-semibold hover:text-primary-300 transition-colors duration-300 group/link" // Added group/link for specific hover
              >
                Access resources
                <ArrowRight className="ml-1.5 w-4 h-4 transition-transform duration-300 group-hover/link:translate-x-1" />
              </a>
            </div>
          ))}
        </div>
        
        <div className="mt-16 text-center">
          <a 
            href="#" 
            className="cta-primary" 
          >
            View All Resources
          </a>
        </div>
      </div>
    </section>
  );
};

export default ResourcesSection;
