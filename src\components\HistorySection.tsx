import React from 'react';
import { MapPin, Calendar, Heart, Film, Church, Award } from 'lucide-react';

const HistorySection: React.FC = () => {
  const timelineEvents = [
    {
      year: "Mid-1970s",
      location: "Delta State, Nigeria",
      icon: <MapPin className="w-6 h-6" />,
      title: "The Beginning",
      description: "During his teenage years, <PERSON><PERSON><PERSON><PERSON> struggled with drug addiction, becoming not just an addict but experiencing severe mental health challenges.",
      color: "from-red-500 to-orange-500"
    },
    {
      year: "The Turning Point",
      location: "Cannabis Shop Incident",
      icon: <Heart className="w-6 h-6" />,
      title: "Divine Intervention",
      description: "After consuming poisoned cannabis, <PERSON> experienced a life-changing moment when a divine voice prevented him from public humiliation and guided him toward safety.",
      color: "from-blue-500 to-purple-500"
    },
    {
      year: "Journey to Healing",
      location: "Onitsha & Aboh, Delta State",
      icon: <Church className="w-6 h-6" />,
      title: "Spiritual Recovery",
      description: "<PERSON> sought help at the Eternal Sacred Order of Cherubim and Ser<PERSON><PERSON>, first in Onitsha, then in Aboh under Apostle <PERSON>.",
      color: "from-green-500 to-teal-500"
    },
    {
      year: "16 Months Later",
      location: "Aboh Branch Church",
      icon: <Award className="w-6 h-6" />,
      title: "Complete Restoration",
      description: "Through prayers, spiritual ministrations, daily Bible studies, and spiritual exercises, Chuck was completely healed of insanity, became drug-free, and was freed from occultism.",
      color: "from-purple-500 to-pink-500"
    },
    {
      year: "2022-2023",
      location: "Film Production",
      icon: <Film className="w-6 h-6" />,
      title: "Raising Awareness",
      description: "Chuck produced a film to expose the dangers of drug abuse and sex trafficking, drawing from his personal experiences and ongoing battle for freedom.",
      color: "from-indigo-500 to-blue-500"
    },
    {
      year: "2024",
      location: "Global Mission",
      icon: <Calendar className="w-6 h-6" />,
      title: "MADAST Founded",
      description: "Ministers Against Drug Abuse and Sex Trafficking was officially founded, born from Chuck's bitter experiences and his commitment to raising awareness about these devastating issues.",
      color: "from-emerald-500 to-green-500"
    }
  ];

  return (
    <section className="py-16 md:py-24 bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 relative overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0">
        <div className="absolute top-0 left-0 w-72 h-72 bg-blue-200/20 rounded-full -translate-x-36 -translate-y-36"></div>
        <div className="absolute bottom-0 right-0 w-96 h-96 bg-purple-200/20 rounded-full translate-x-48 translate-y-48"></div>
        <div className="absolute top-1/2 left-1/2 w-64 h-64 bg-green-200/10 rounded-full -translate-x-32 -translate-y-32"></div>
      </div>

      <div className="container mx-auto px-6 relative z-10">
        {/* Section Header */}
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-bold text-gray-800 mb-6">
            History of the Founding of 
            <span className="text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-purple-600"> MADAST</span>
          </h2>
          <p className="text-xl text-gray-600 max-w-4xl mx-auto leading-relaxed">
            The founding of Ministers Against Drug Abuse and Sex Trafficking (MADAST) was inspired by the transformative experiences and dedicated work of our Founder, Rev. Chukwudi Chuck Eke.
          </p>
        </div>

        {/* Timeline */}
        <div className="relative">
          {/* Central Timeline Line */}
          <div className="absolute left-1/2 transform -translate-x-1/2 w-1 h-full bg-gradient-to-b from-blue-500 via-purple-500 to-green-500 rounded-full hidden lg:block"></div>

          {/* Timeline Events */}
          <div className="space-y-12 lg:space-y-16">
            {timelineEvents.map((event, index) => (
              <div key={index} className={`flex flex-col lg:flex-row items-center gap-8 ${index % 2 === 0 ? 'lg:flex-row' : 'lg:flex-row-reverse'}`}>
                {/* Content Card */}
                <div className={`flex-1 ${index % 2 === 0 ? 'lg:text-right lg:pr-8' : 'lg:text-left lg:pl-8'}`}>
                  <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-8 shadow-xl border border-white/50 hover:shadow-2xl transform hover:-translate-y-2 transition-all duration-300">
                    <div className={`inline-flex items-center gap-3 mb-4 ${index % 2 === 0 ? 'lg:flex-row-reverse' : 'lg:flex-row'}`}>
                      <div className={`p-3 rounded-xl bg-gradient-to-r ${event.color} text-white shadow-lg`}>
                        {event.icon}
                      </div>
                      <div>
                        <h3 className="text-xl font-bold text-gray-800">{event.title}</h3>
                        <p className="text-sm font-medium text-gray-500">{event.year} • {event.location}</p>
                      </div>
                    </div>
                    <p className="text-gray-600 leading-relaxed">{event.description}</p>
                  </div>
                </div>

                {/* Timeline Node */}
                <div className="relative lg:flex-shrink-0 hidden lg:block">
                  <div className={`w-6 h-6 rounded-full bg-gradient-to-r ${event.color} border-4 border-white shadow-lg z-10 relative`}></div>
                  <div className={`absolute inset-0 w-6 h-6 rounded-full bg-gradient-to-r ${event.color} animate-ping opacity-20`}></div>
                </div>

                {/* Spacer for alternating layout */}
                <div className="flex-1 hidden lg:block"></div>
              </div>
            ))}
          </div>
        </div>

        {/* Bottom Section */}
        <div className="mt-20 text-center">
          <div className="bg-white/60 backdrop-blur-sm rounded-2xl p-8 md:p-12 shadow-xl border border-white/50 max-w-4xl mx-auto">
            <h3 className="text-2xl md:text-3xl font-bold text-gray-800 mb-6">From Personal Struggle to Global Mission</h3>
            <p className="text-lg text-gray-600 leading-relaxed mb-8">
              MADAST was founded from the profound personal experiences of Rev. Chukwudi Eke and his commitment to producing awareness content about the devastating impact of drug abuse and sex trafficking in our global community. What began as a personal battle became a mission to help others find freedom and healing.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <a 
                href="/about" 
                className="inline-flex items-center justify-center px-8 py-4 bg-gradient-to-r from-blue-600 to-purple-600 text-white font-semibold rounded-xl shadow-lg hover:shadow-xl transform hover:-translate-y-1 transition-all duration-300"
              >
                Learn More About Our Mission
              </a>
              <a 
                href="/get-involved" 
                className="inline-flex items-center justify-center px-8 py-4 bg-white/80 backdrop-blur-sm text-gray-800 font-semibold rounded-xl shadow-lg hover:shadow-xl border border-gray-200 transform hover:-translate-y-1 transition-all duration-300"
              >
                Join Our Cause
              </a>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default HistorySection;
