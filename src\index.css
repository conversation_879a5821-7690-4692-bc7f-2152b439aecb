@tailwind base;
@tailwind components;
@tailwind utilities;

@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

:root {
  font-family: 'Inter', sans-serif;
  line-height: 1.5;
  font-weight: 400;
}

body {
  margin: 0;
  min-width: 320px;
  min-height: 100vh;
}

/* Custom utility classes */
@layer utilities {
  .transition-transform {
    transition-property: transform;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 150ms;
  }

  /* Removed .animate-headline-fade and @keyframes fadeIn */

  .text-gradient-animated {
    @apply bg-clip-text text-transparent;
    /* Dark Blue (#2563EB) -> Purple (#8B5CF6) -> Darker Purple-Pink (#BE185D) */
    background-image: linear-gradient(90deg, #2563EB, #8B5CF6, #BE185D, #8B5CF6, #2563EB); /* Added intermediate steps for smoother loop */
    background-size: 300% 300%; /* Increased size for more color travel */
    /* animation: textGradientLoop 3s linear infinite; */ /* Animation removed */
  }

  @keyframes textGradientLoop {
    0% {
      background-position: 0% center;
    }
    50% {
      background-position: 100% center;
    }
    100% {
      background-position: 0% center;
    }
  }

  .text-gradient-silver {
    @apply bg-clip-text text-transparent;
    /* White (#FFFFFF) -> Gray (#A0AEC0) -> Off-White (#F7FAFC) */
    background-image: linear-gradient(90deg, #FFFFFF, #A0AEC0, #F7FAFC, #A0AEC0, #FFFFFF);
    background-size: 300% 300%;
    /* animation: textGradientSilverLoop 4s linear infinite; */ /* Animation removed */
  }

  @keyframes textGradientSilverLoop {
    0% {
      background-position: 0% center;
    }
    50% {
      background-position: 100% center;
    }
    100% {
      background-position: 0% center;
    }
  }

  .cta-primary {
    @apply px-8 py-3 rounded-full font-semibold text-white transition-all duration-300 transform;
    background-image: linear-gradient(to right, #8B5CF6, #EC4899); /* purple to magenta */
    box-shadow: inset 0 2px 4px 0 rgba(0, 0, 0, 0.2); /* subtle inner shadow */
  }

  .cta-primary:hover {
    transform: scale(1.05);
    background-image: linear-gradient(to right, #A78BFA, #F472B6); /* lighter gradient */
    box-shadow: inset 0 2px 4px 0 rgba(0, 0, 0, 0.2), 0 0 10px rgba(236, 72, 153, 0.3); /* inner shadow + subtle glow */
  }
  
  .cta-secondary {
    @apply px-8 py-3 rounded-full font-semibold transition-all duration-300 transform border-2 border-white text-white;
  }

  .cta-secondary:hover {
    transform: scale(1.05);
    background-color: rgba(255, 255, 255, 0.1);
     box-shadow: 0 0 15px rgba(255, 255, 255, 0.3); /* soft glow for secondary */
  }

  .title-underline-white {
    position: relative;
    display: inline-block;
    padding-bottom: 10px; /* Space for the underline */
  }
  .title-underline-white::after {
    content: '';
    position: absolute;
    bottom: 0; /* At the bottom of the padding area */
    left: 50%;
    transform: translateX(-50%);
    width: 70px;  /* Width of the underline */
    height: 3px; /* Thickness of the underline */
    background-color: white;
    border-radius: 2px; 
  }

  .nav-donate-glow {
    box-shadow: 0 0 8px 2px rgba(255, 215, 0, 0.4), /* Softer Gold glow */
                0 0 12px 4px rgba(255, 215, 0, 0.2); /* Reduced intensity */
    transition: box-shadow 0.3s ease-in-out;
  }
}
