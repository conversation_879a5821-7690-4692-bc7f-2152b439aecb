import React from 'react';
import { Twitter, Facebook, Linkedin, Mail, MapPin, Phone } from 'lucide-react';

const Footer: React.FC = () => {
  return (
    <footer className="bg-neutral-900 text-white pt-16 pb-8">
      <div className="container mx-auto px-4">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8 mb-12">
          <div className="md:col-span-2">
            <h3 className="text-xl font-bold mb-4">
              Drug Abuse and Sexual Trafficking<br />
              Foundation (DAST)
            </h3>
            <p className="text-neutral-300 mb-6 max-w-md">
              Empowering faith leaders to combat domestic abuse and human trafficking through education, resources, and community action.
            </p>
          </div>
          
          <div>
            <h4 className="text-lg font-medium mb-4">Quick Links</h4>
            <ul className="space-y-2">
              <li><a href="#" className="text-neutral-300 hover:text-white transition-colors duration-300">Home</a></li>
              <li><a href="#" className="text-neutral-300 hover:text-white transition-colors duration-300">About</a></li>
              <li><a href="#" className="text-neutral-300 hover:text-white transition-colors duration-300">Programs</a></li>
              <li><a href="#" className="text-neutral-300 hover:text-white transition-colors duration-300">Resources</a></li>
              <li><a href="#" className="text-neutral-300 hover:text-white transition-colors duration-300">Get Involved</a></li>
              <li><a href="#" className="text-neutral-300 hover:text-white transition-colors duration-300">Contact</a></li>
            </ul>
          </div>
          
          <div>
            <h4 className="text-lg font-medium mb-4">Contact Us</h4>
            <ul className="space-y-3">
              <li className="flex items-start">
                <MapPin size={18} className="mr-2 mt-1 text-primary-400" />
                <span className="text-neutral-300">2121 Washington Street, <br />Boston, MA 02119, <br />United States</span>
              </li>
              <li className="flex items-center">
                <Mail size={18} className="mr-2 text-primary-400" />
                <a href="mailto:<EMAIL>" className="text-neutral-300 hover:text-white transition-colors duration-300">
                  <EMAIL>
                </a>
              </li>
              <li className="flex items-center">
                <Phone size={18} className="mr-2 text-primary-400" />
                <a href="tel:************" className="text-neutral-300 hover:text-white transition-colors duration-300">
                  ************
                </a>
              </li>
            </ul>
          </div>
        </div>
        
        <div className="border-t border-neutral-800 pt-8 mt-8">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <p className="text-neutral-400 text-sm mb-4 md:mb-0">
              &copy; {new Date().getFullYear()} Drug Abuse and Sexual Trafficking Foundation. All rights reserved.
            </p>
            <div className="flex space-x-6">
              <a href="#" className="text-neutral-400 hover:text-white text-sm transition-colors duration-300">Privacy Policy</a>
              <a href="#" className="text-neutral-400 hover:text-white text-sm transition-colors duration-300">Terms of Service</a>
              <a href="#" className="text-neutral-400 hover:text-white text-sm transition-colors duration-300">Cookie Policy</a>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
