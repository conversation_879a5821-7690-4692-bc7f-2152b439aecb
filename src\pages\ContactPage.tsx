import React, { useState, useRef } from 'react';
import { Phone, Mail, MapPin, Clock, Send, AlertTriangle, CheckCircle, XCircle } from 'lucide-react';
import emailjs from '@emailjs/browser';
// import ReCAPTCHA from 'react-google-recaptcha';
import { EMAIL_CONFIG } from '../config/email'; // Added AlertTriangle

const ContactPage: React.FC = () => {
  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    email: '',
    subject: '',
    message: ''
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitStatus, setSubmitStatus] = useState<'idle' | 'success' | 'error'>('idle');
  const [submitMessage, setSubmitMessage] = useState('');
  const recaptchaRef = useRef<any>(null);

  // EmailJS configuration
  const EMAILJS_SERVICE_ID = EMAIL_CONFIG.EMAILJS_SERVICE_ID;
  const EMAILJS_TEMPLATE_ID = EMAIL_CONFIG.EMAILJS_TEMPLATE_ID;
  const EMAILJS_PUBLIC_KEY = EMAIL_CONFIG.EMAILJS_PUBLIC_KEY;
  const RECAPTCHA_SITE_KEY = EMAIL_CONFIG.RECAPTCHA_SITE_KEY;

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    setSubmitStatus('idle');

    try {
      // Verify reCAPTCHA
      const recaptchaValue = recaptchaRef.current?.getValue();
      if (!recaptchaValue) {
        setSubmitStatus('error');
        setSubmitMessage('Please complete the reCAPTCHA verification.');
        setIsSubmitting(false);
        return;
      }

      // Validate form
      if (!formData.firstName || !formData.lastName || !formData.email || !formData.subject || !formData.message) {
        setSubmitStatus('error');
        setSubmitMessage('Please fill in all required fields.');
        setIsSubmitting(false);
        return;
      }

      // Send email using EmailJS
      const templateParams = {
        from_name: `${formData.firstName} ${formData.lastName}`,
        from_email: formData.email,
        subject: formData.subject,
        message: formData.message,
        to_name: 'MADAST Team',
      };

      // Send email via EmailJS
      await emailjs.send(EMAILJS_SERVICE_ID, EMAILJS_TEMPLATE_ID, templateParams, EMAILJS_PUBLIC_KEY);

      setSubmitStatus('success');
      setSubmitMessage('Thank you for your message! We will get back to you within 24 hours.');

      // Reset form
      setFormData({
        firstName: '',
        lastName: '',
        email: '',
        subject: '',
        message: ''
      });

      // Reset reCAPTCHA
      recaptchaRef.current?.reset();

    } catch (error) {
      console.error('Error sending email:', error);
      setSubmitStatus('error');
      setSubmitMessage('Sorry, there was an error sending your message. Please try again or contact us directly.');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <>
      {/* Hero Section - Enhanced */}
      <section className="relative text-white py-24 md:py-32 bg-cover bg-center" style={{ backgroundImage: "url('/images/a5.jpg')" }}>
        <div className="absolute inset-0 bg-black opacity-50"></div> {/* Overlay for contrast */}
        <div className="container mx-auto px-6 text-center relative z-10"> {/* Ensure content is above overlay */}
          <h1 className="text-4xl sm:text-5xl md:text-6xl font-bold mb-6 leading-tight" style={{ textShadow: '2px 2px 8px rgba(0,0,0,0.5)' }}>
            Contact Us
          </h1>
          <p className="text-lg md:text-xl max-w-3xl mx-auto text-neutral-100 leading-relaxed" style={{ textShadow: '1px 1px 3px rgba(0,0,0,0.4)' }}> {/* Adjusted shadow for better visibility */}
            We're here to help, answer your questions, and connect you with the resources you need. Reach out to our dedicated team today.
          </p>
        </div>
      </section>

      {/* Contact Information - Enhanced */}
      <section className="py-16 md:py-20 bg-white">
        <div className="container mx-auto px-6">
          <div className="text-center mb-14 md:mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-800 title-underline-white mb-3">Get in Touch</h2> {/* Removed text-gradient-animated */}
            <p className="text-lg text-neutral-600 mt-2 max-w-3xl mx-auto leading-relaxed">
              Choose the most convenient way to reach us. We look forward to hearing from you.
            </p>
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-8">
            {[
              { icon: <Phone size={28} className="text-primary-600" />, title: "Phone", lines: ["************"], note: "Call us during business hours." },
              { icon: <Mail size={28} className="text-primary-600" />, title: "Email", lines: ["<EMAIL>"], note: "We'll respond as soon as possible." },
              { icon: <MapPin size={28} className="text-primary-600" />, title: "Main Office", lines: ["2121 Washington Street", "Boston, MA 02119", "United States"], note: "Our primary operational base." },
              { icon: <Clock size={28} className="text-primary-600" />, title: "Office Hours", lines: ["Mon - Fri: 9 AM - 6 PM", "Saturday: 10 AM - 4 PM", "Sunday: Closed"], note: "Crisis line available 24/7." }
            ].map(contact => (
              <div key={contact.title} className="text-center p-6 bg-slate-50 rounded-xl shadow-lg hover:shadow-xl transition-shadow duration-300 border border-slate-200 flex flex-col">
                <div className="flex justify-center mb-4">
                  <div className="p-3 bg-primary-100 rounded-full">{contact.icon}</div>
                </div>
                <h3 className="text-xl font-semibold text-neutral-800 mb-2">{contact.title}</h3>
                <div className="space-y-1 mb-3 text-neutral-700 flex-grow">
                  {contact.lines.map((line, i) => <p key={i} className="text-sm">{line}</p>)}
                </div>
                <p className="text-xs text-neutral-500 mt-auto">{contact.note}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Contact Form & Office Locations - Enhanced */}
      <section id="contact-form-section" className="py-16 md:py-20 bg-slate-100"> {/* Added ID here */}
        <div className="container mx-auto px-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 lg:gap-16 items-start">
            {/* Contact Form Section */}
            <div className="bg-white p-8 md:p-10 rounded-xl shadow-2xl border border-gray-200/80">
              <div className="text-center lg:text-left mb-8">
                <h2 className="text-3xl md:text-4xl font-bold text-gray-800 title-underline-white mb-3 inline-block">Send Us a Message</h2> {/* Removed text-gradient-animated */}
                 <p className="text-lg text-neutral-600 mt-2 leading-relaxed">
                    Have a question or need to get in touch? Fill out the form below. For urgent matters, please use our emergency contact information.
                 </p>
              </div>

              {/* Success/Error Messages */}
              {submitStatus !== 'idle' && (
                <div className={`p-4 rounded-lg mb-6 flex items-center ${
                  submitStatus === 'success'
                    ? 'bg-green-50 border border-green-200 text-green-800'
                    : 'bg-red-50 border border-red-200 text-red-800'
                }`}>
                  {submitStatus === 'success' ? (
                    <CheckCircle size={20} className="mr-3 flex-shrink-0" />
                  ) : (
                    <XCircle size={20} className="mr-3 flex-shrink-0" />
                  )}
                  <p className="text-sm">{submitMessage}</p>
                </div>
              )}

              <form onSubmit={handleSubmit} className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label htmlFor="firstName" className="block text-sm font-medium text-neutral-700 mb-1.5">First Name *</label>
                    <input
                      type="text"
                      id="firstName"
                      name="firstName"
                      value={formData.firstName}
                      onChange={handleInputChange}
                      required
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-shadow"
                      placeholder="Your first name"
                    />
                  </div>
                  <div>
                    <label htmlFor="lastName" className="block text-sm font-medium text-neutral-700 mb-1.5">Last Name *</label>
                    <input
                      type="text"
                      id="lastName"
                      name="lastName"
                      value={formData.lastName}
                      onChange={handleInputChange}
                      required
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-shadow"
                      placeholder="Your last name"
                    />
                  </div>
                </div>
                <div>
                  <label htmlFor="email" className="block text-sm font-medium text-neutral-700 mb-1.5">Email Address *</label>
                  <input
                    type="email"
                    id="email"
                    name="email"
                    value={formData.email}
                    onChange={handleInputChange}
                    required
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-shadow"
                    placeholder="<EMAIL>"
                  />
                </div>
                <div>
                  <label htmlFor="subject" className="block text-sm font-medium text-neutral-700 mb-1.5">Subject *</label>
                  <select
                    id="subject"
                    name="subject"
                    value={formData.subject}
                    onChange={handleInputChange}
                    required
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 bg-white transition-shadow"
                  >
                    <option value="">Select a subject</option>
                    <option value="general">General Inquiry</option>
                    <option value="volunteer">Volunteer Opportunities</option>
                    <option value="support">Request Support</option>
                    <option value="partnership">Partnership</option>
                    <option value="media">Media Inquiry</option>
                  </select>
                </div>
                <div>
                  <label htmlFor="message" className="block text-sm font-medium text-neutral-700 mb-1.5">Message *</label>
                  <textarea
                    id="message"
                    name="message"
                    rows={5}
                    value={formData.message}
                    onChange={handleInputChange}
                    required
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-shadow"
                    placeholder="Tell us how we can help you..."
                  ></textarea>
                </div>
                {/* reCAPTCHA */}
                <div className="flex justify-center">
                  {/* Uncomment when reCAPTCHA is properly configured */}
                  {/* <ReCAPTCHA
                    ref={recaptchaRef}
                    sitekey={RECAPTCHA_SITE_KEY}
                    theme="light"
                  /> */}
                  <div className="bg-gray-100 border-2 border-dashed border-gray-300 rounded-lg p-4 text-center text-gray-500">
                    <p className="text-sm">reCAPTCHA will be enabled here</p>
                    <p className="text-xs mt-1">Configure RECAPTCHA_SITE_KEY to activate</p>
                  </div>
                </div>

                <button
                  type="submit"
                  disabled={isSubmitting}
                  className={`w-full text-base py-3.5 px-6 flex items-center justify-center rounded-lg font-semibold transition-all duration-300 ${
                    isSubmitting
                      ? 'bg-gray-400 cursor-not-allowed'
                      : 'cta-primary hover:shadow-lg transform hover:-translate-y-0.5'
                  }`}
                >
                  {isSubmitting ? (
                    <>
                      <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2.5"></div>
                      Sending...
                    </>
                  ) : (
                    <>
                      <Send size={18} className="mr-2.5" />
                      Send Message
                    </>
                  )}
                </button>
              </form>
            </div>

            {/* Office Locations & Emergency Notice */}
            <div className="space-y-8 lg:pt-4">
               <div className="bg-white p-6 md:p-8 rounded-xl shadow-2xl border border-gray-200/80">
                <div className="text-center lg:text-left mb-6">
                    <h2 className="text-3xl md:text-4xl font-bold text-gray-800 title-underline-white mb-3 inline-block">Nigeria Liaison Offices</h2> {/* Removed text-gradient-animated */}
                    <p className="text-lg text-neutral-600 mt-2 leading-relaxed">
                        Our liaison offices in Nigeria extending our reach and support.
                    </p>
                </div>
                <div className="space-y-6">
                  {[
                    { title: "Abuja Liaison Office", address: "2 Nwanna Close, Wuse Zone 3, Abuja, F.C.T, Nigeria." },
                    { title: "Lagos Liaison Office (Ogun State)", address: "Neighborhood Church of God. Missionary Center, #28 Missionary Street, Igbusi, Iyana Ilogbo, Sango, Ogun State, Nigeria." },
                    { title: "Delta State Liaison Office", address: "6 Sir Ogagifo Street, DDPA Estate, Asaba, Delta State, Nigeria." },
                    { title: "Benin Liaison Office (Edo State)", address: "God's Zion Maternity, 23 Ekpenede Street, off Plymouth Road, Benin, Edo State, Nigeria." }
                  ].map(office => (
                    <div key={office.title} className="bg-slate-50 rounded-lg p-4 border border-slate-200">
                      <h3 className="text-lg font-semibold text-neutral-800 mb-1.5">{office.title}</h3>
                      <p className="text-sm text-neutral-600 flex items-start">
                        <MapPin size={16} className="mr-2 mt-0.5 text-primary-500 flex-shrink-0" />
                        <span>{office.address}</span>
                      </p>
                    </div>
                  ))}
                </div>
              </div>
              
              <div className="bg-red-50 border-l-4 border-red-600 p-6 rounded-lg shadow-lg">
                <div className="flex items-center mb-3">
                    <AlertTriangle size={24} className="text-red-600 mr-3 flex-shrink-0" />
                    <h3 className="text-lg font-bold text-red-800">Emergency Assistance</h3>
                </div>
                <p className="text-red-700 mb-3 text-sm">
                  If you or someone you know is in immediate danger, please contact:
                </p>
                <p className="text-red-800 font-semibold text-md">911 (Emergency Services)</p>
                <p className="text-red-800 font-semibold text-md">1-888-373-7888 (Trafficking Hotline)</p>
              </div>
            </div>
          </div>
        </div>
      </section>
    </>
  );
};

export default ContactPage;
